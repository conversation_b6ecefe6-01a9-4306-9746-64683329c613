/* src/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.flash-highlight {
    animation: flash-animation 1s ease-out;
}

@keyframes flash-animation {
    0% {
        background-color: rgba(255, 215, 0, 0.8);
    }
    100% {
        background-color: rgba(255, 215, 0, 0.1);
    }
}

.highlight-indicator {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #ffd700;
    border-radius: 8px;
    pointer-events: none;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

/* API端点轮播动画 - 向上翻动效果 */
.api-endpoint-container {
    position: relative;
    overflow: hidden;
    height: 1.2em;
    display: inline-block;
    min-width: 160px;
}

.api-endpoint-text {
    position: absolute;
    right: 0;
    top: 0;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    color: #3b82f6;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
}

.api-endpoint-text.slide-out {
    transform: translateY(-120%);
    opacity: 0;
}

.api-endpoint-text.slide-in {
    transform: translateY(120%);
    opacity: 0;
}

.api-endpoint-text.active {
    transform: translateY(0);
    opacity: 1;
}

/* 添加淡入淡出效果 */
.api-endpoint-text {
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .api-endpoint-container {
        min-width: auto;
        text-align: center;
        height: 1em;
    }

    .api-endpoint-text {
        font-size: 12px;
        position: relative;
        right: auto;
    }
}

