/* src/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.flash-highlight {
    animation: flash-animation 1s ease-out;
}

@keyframes flash-animation {
    0% {
        background-color: rgba(255, 215, 0, 0.8);
    }
    100% {
        background-color: rgba(255, 215, 0, 0.1);
    }
}

.highlight-indicator {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #ffd700;
    border-radius: 8px;
    pointer-events: none;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

/* API地址卡片样式优化 - 响应式设计 */
.api-address-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.api-address-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.api-address-input {
    background: white;
    border: 1px solid #e5e7eb;
    border-right: none;
    border-radius: 12px 0 0 12px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    transition: all 0.2s ease;
}

.api-endpoint-display {
    color: #3b82f6;
    font-weight: 600;
    font-size: 14px;
    min-width: 160px;
    text-align: right;
    animation: fadeInOut 3s infinite;
}

@keyframes fadeInOut {
    0%, 90%, 100% { opacity: 1; }
    45%, 55% { opacity: 0.7; }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .api-address-card {
        border-radius: 12px;
        margin: 0 8px;
    }

    .api-endpoint-display {
        font-size: 12px;
        min-width: auto;
        text-align: center;
    }

    /* 移动端文字换行优化 */
    .mobile-break-text {
        word-break: break-all;
        line-height: 1.4;
    }
}

