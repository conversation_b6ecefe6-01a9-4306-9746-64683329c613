/* src/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.flash-highlight {
    animation: flash-animation 1s ease-out;
}

@keyframes flash-animation {
    0% {
        background-color: rgba(255, 215, 0, 0.8);
    }
    100% {
        background-color: rgba(255, 215, 0, 0.1);
    }
}

.highlight-indicator {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #ffd700;
    border-radius: 8px;
    pointer-events: none;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

/* 闪光文字效果 - 参考new-api */
@keyframes sweep-shine {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.shine-text {
    background: linear-gradient(90deg, currentColor 0%, currentColor 40%, rgba(255, 255, 255, 0.9) 50%, currentColor 60%, currentColor 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: sweep-shine 4s linear infinite;
}

/* 背景模糊球效果 - 参考new-api */
.blur-ball {
    position: absolute;
    width: 360px;
    height: 360px;
    border-radius: 50%;
    filter: blur(120px);
    pointer-events: none;
    z-index: -1;
}

.blur-ball-indigo {
    background: #6366f1; /* indigo-500 */
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0.25;
}

.blur-ball-teal {
    background: #14b8a6; /* teal-400 */
    top: 200px;
    left: 30%;
    opacity: 0.2;
}